package com.alpha.gallery.feature.mediaviewer.components

import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiState
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiEvent
import com.alpha.gallery.feature.mediaviewer.model.SwipeDirection
import kotlinx.coroutines.launch

/**
 * Main content component for the media viewer with horizontal paging
 */
@OptIn(ExperimentalFoundationApi::class, ExperimentalSharedTransitionApi::class)
@Composable
fun MediaViewerContent(
    uiState: MediaViewerUiState,
    onEvent: (MediaViewerUiEvent) -> Unit,
    modifier: Modifier = Modifier,
    sharedTransitionScope: SharedTransitionScope? = null,
    animatedVisibilityScope: AnimatedVisibilityScope? = null
) {
    val pagerState = rememberPagerState(
        initialPage = uiState.currentIndex,
        pageCount = { uiState.mediaItems.size }
    )
    val coroutineScope = rememberCoroutineScope()
    
    // Sync pager state with UI state
    LaunchedEffect(uiState.currentIndex) {
        if (pagerState.currentPage != uiState.currentIndex) {
            pagerState.animateScrollToPage(uiState.currentIndex)
        }
    }
    
    // Handle page changes
    LaunchedEffect(pagerState.currentPage) {
        if (pagerState.currentPage != uiState.currentIndex) {
            onEvent(MediaViewerUiEvent.NavigateToIndex(pagerState.currentPage))
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { pageIndex ->
            val mediaItem = uiState.mediaItems.getOrNull(pageIndex)
            
            if (mediaItem != null) {
                MediaViewerPage(
                    mediaItem = mediaItem,
                    isCurrentPage = pageIndex == uiState.currentIndex,
                    uiState = uiState,
                    onEvent = onEvent,
                    modifier = Modifier.fillMaxSize(),
                    sharedTransitionScope = sharedTransitionScope,
                    animatedVisibilityScope = animatedVisibilityScope
                )
            } else {
                // Error state for missing media item
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = "Error loading media",
                        tint = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                }
            }
        }
        
        // Page indicator (optional, for debugging or when needed)
        if (uiState.mediaItems.size > 1 && uiState.isControlsVisible) {
            PageIndicator(
                currentPage = uiState.currentIndex,
                totalPages = uiState.mediaItems.size,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 100.dp) // Above bottom controls
            )
        }
    }
}

/**
 * Individual page in the media viewer pager
 */
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun MediaViewerPage(
    mediaItem: MediaItem,
    isCurrentPage: Boolean,
    uiState: MediaViewerUiState,
    onEvent: (MediaViewerUiEvent) -> Unit,
    modifier: Modifier = Modifier,
    sharedTransitionScope: SharedTransitionScope? = null,
    animatedVisibilityScope: AnimatedVisibilityScope? = null
) {
    // Apply shared element transitions if available
    val boxModifier = if (sharedTransitionScope != null && animatedVisibilityScope != null) {
        with(sharedTransitionScope) {
            modifier
                .fillMaxWidth()
                .aspectRatio(mediaItem.aspectRatio)
                .sharedBounds(
                    rememberSharedContentState("card-${mediaItem.id}"),
                    animatedVisibilityScope = animatedVisibilityScope,
                    boundsTransform = { _, _ ->
                        tween(durationMillis = 520, easing = FastOutSlowInEasing)
                    }
                )
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { onEvent(MediaViewerUiEvent.OnSingleTap) },
                        onDoubleTap = { onEvent(MediaViewerUiEvent.OnDoubleTap) }
                    )
                }
        }
    } else {
        modifier
            .fillMaxWidth()
            .aspectRatio(mediaItem.aspectRatio)
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onEvent(MediaViewerUiEvent.OnSingleTap) },
                    onDoubleTap = { onEvent(MediaViewerUiEvent.OnDoubleTap) }
                )
            }
    }

    Box(modifier = boxModifier) {
        if (mediaItem.isVideo) {
            // Video content
            VideoPlayer(
                mediaItem = mediaItem,
                isPlaying = isCurrentPage && uiState.isVideoPlaying,
                position = if (isCurrentPage) uiState.videoPosition else 0L,
                duration = if (isCurrentPage) uiState.videoDuration else mediaItem.duration,
                onPlayPause = { onEvent(MediaViewerUiEvent.ToggleVideoPlayback) },
                onSeek = { position -> onEvent(MediaViewerUiEvent.SeekVideo(position)) },
                onPositionChanged = { position -> 
                    if (isCurrentPage) {
                        onEvent(MediaViewerUiEvent.OnVideoPositionChanged(position))
                    }
                },
                onDurationChanged = { duration ->
                    if (isCurrentPage) {
                        onEvent(MediaViewerUiEvent.OnVideoDurationChanged(duration))
                    }
                },
                onSingleTap = { onEvent(MediaViewerUiEvent.OnSingleTap) },
                onDoubleTap = { onEvent(MediaViewerUiEvent.OnDoubleTap) },
                modifier = Modifier.fillMaxSize()
            )
        } else {
            // Image content
            ZoomableImage(
                mediaItem = mediaItem,
                onSingleTap = { onEvent(MediaViewerUiEvent.OnSingleTap) },
                onDoubleTap = { onEvent(MediaViewerUiEvent.OnDoubleTap) },
                onZoomChange = { scale ->
                    if (isCurrentPage) {
                        onEvent(MediaViewerUiEvent.OnImageZoom(scale))
                    }
                },
                contentScale = ContentScale.Fit,
                modifier = Modifier.fillMaxSize(),
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = animatedVisibilityScope
            )
        }
        
        // Zoom level indicator for images
        if (!mediaItem.isVideo && isCurrentPage) {
            ZoomLevelIndicator(
                zoomLevel = uiState.zoomScale,
                visible = uiState.isControlsVisible && uiState.isZoomed,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
            )
        }
    }
}

/**
 * Page indicator showing current position
 */
@Composable
fun PageIndicator(
    currentPage: Int,
    totalPages: Int,
    modifier: Modifier = Modifier
) {
    if (totalPages <= 1) return
    
    Surface(
        modifier = modifier,
        color = Color.Black.copy(alpha = 0.5f),
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp)
    ) {
        Text(
            text = "${currentPage + 1} / $totalPages",
            color = Color.White,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
}

/**
 * Error state for missing media items
 */
@Composable
fun MediaItemErrorState(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = "Error",
                tint = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Media not found",
                color = Color.White.copy(alpha = 0.7f),
                style = MaterialTheme.typography.bodyLarge
            )
            
            Text(
                text = "This media item could not be loaded",
                color = Color.White.copy(alpha = 0.5f),
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * Gesture overlay for handling swipe gestures
 */
@Composable
fun GestureOverlay(
    onSwipeHorizontal: (SwipeDirection) -> Unit,
    onSwipeVertical: (SwipeDirection) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                // Custom gesture detection for swipe directions
                // This would need more sophisticated gesture detection
                // For now, we rely on the pager's built-in horizontal swiping
            }
    )
}

/**
 * Preloader for adjacent media items
 */
@Composable
fun MediaPreloader(
    mediaItems: List<MediaItem>,
    currentIndex: Int,
    preloadCount: Int = 2
) {
    // This component would handle preloading of adjacent media items
    // Implementation would depend on your image loading library (Coil, Glide, etc.)
    // For now, this is a placeholder
    
    LaunchedEffect(currentIndex, mediaItems) {
        val startIndex = maxOf(0, currentIndex - preloadCount)
        val endIndex = minOf(mediaItems.size - 1, currentIndex + preloadCount)
        
        for (i in startIndex..endIndex) {
            if (i != currentIndex) {
                val mediaItem = mediaItems[i]
                // Preload logic here
                // For images: preload with Coil
                // For videos: preload thumbnail or first frame
            }
        }
    }
}
