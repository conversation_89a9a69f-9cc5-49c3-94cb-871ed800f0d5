package com.alpha.gallery.feature.mediaviewer

import androidx.activity.compose.BackHandler
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerContent
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerTopBar
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerBottomBar
import com.alpha.gallery.feature.mediaviewer.components.MediaInfoOverlay
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerLoadingState
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerErrorState
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiEvent
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiAction

/**
 * Main Media Viewer Screen
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalSharedTransitionApi::class)
@Composable
fun MediaViewerScreen(
    startingItemId: String,
    onNavigateBack: () -> Unit = {},
    onShowMessage: (String) -> Unit = {},
    onShowError: (String) -> Unit = {},
    sharedTransitionScope: SharedTransitionScope? = null,
    animatedVisibilityScope: AnimatedVisibilityScope? = null,
    viewModel: MediaViewerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // Load media items when screen is first displayed
    LaunchedEffect(startingItemId) {
        viewModel.onEvent(MediaViewerUiEvent.LoadMediaItems(startingItemId))
    }

    // Handle UI actions
    LaunchedEffect(Unit) {
        viewModel.uiActions.collect { action ->
            when (action) {
                is MediaViewerUiAction.NavigateBack -> {
                    onNavigateBack()
                }
                is MediaViewerUiAction.ShowMessage -> {
                    onShowMessage(action.message)
                }
                is MediaViewerUiAction.ShowError -> {
                    onShowError(action.error)
                }
                is MediaViewerUiAction.ShareMedia -> {
                    // TODO: Implement share functionality
                }
                is MediaViewerUiAction.DeleteMedia -> {
                    // TODO: Implement delete functionality
                }
                is MediaViewerUiAction.EditMedia -> {
                    // TODO: Implement edit functionality
                }
                is MediaViewerUiAction.SetAsFavorite -> {
                    // TODO: Implement favorite functionality
                }
            }
        }
    }

    // Handle back button
    BackHandler {
        viewModel.onEvent(MediaViewerUiEvent.NavigateBack)
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        when {
            // Show loading state
            uiState.showLoadingState -> {
                MediaViewerLoadingState(
                    modifier = Modifier.fillMaxSize()
                )
            }

            // Show error state
            uiState.showError -> {
                MediaViewerErrorState(
                    error = uiState.error ?: "Unknown error",
                    onRetry = { viewModel.onEvent(MediaViewerUiEvent.RetryLoad) },
                    onBack = { viewModel.onEvent(MediaViewerUiEvent.NavigateBack) },
                    modifier = Modifier.fillMaxSize()
                )
            }

            // Show content
            uiState.showContent -> {
                MediaViewerContent(
                    uiState = uiState,
                    onEvent = viewModel::onEvent,
                    modifier = Modifier.fillMaxSize(),
                    sharedTransitionScope = sharedTransitionScope,
                    animatedVisibilityScope = animatedVisibilityScope
                )

                // Top bar with controls
                AnimatedVisibility(
                    visible = uiState.isControlsVisible && !uiState.isFullscreen,
                    enter = slideInVertically(
                        initialOffsetY = { -it },
                        animationSpec = tween(300)
                    ) + fadeIn(animationSpec = tween(300)),
                    exit = slideOutVertically(
                        targetOffsetY = { -it },
                        animationSpec = tween(300)
                    ) + fadeOut(animationSpec = tween(300)),
                    modifier = Modifier.align(Alignment.TopCenter)
                ) {
                    MediaViewerTopBar(
                        currentItem = uiState.currentMediaItem,
                        currentIndex = uiState.currentIndex,
                        totalCount = uiState.mediaItems.size,
                        onBack = { viewModel.onEvent(MediaViewerUiEvent.NavigateBack) },
                        onShare = {
                            uiState.currentMediaItem?.let { item ->
                                viewModel.onEvent(MediaViewerUiEvent.NavigateBack) // Placeholder
                            }
                        },
                        onFavorite = {
                            uiState.currentMediaItem?.let { item ->
                                viewModel.onEvent(MediaViewerUiEvent.NavigateBack) // Placeholder
                            }
                        },
                        onMore = {
                            // TODO: Show more options menu
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // Bottom bar with navigation controls
                AnimatedVisibility(
                    visible = uiState.isControlsVisible && !uiState.isFullscreen,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(300)
                    ) + fadeIn(animationSpec = tween(300)),
                    exit = slideOutVertically(
                        targetOffsetY = { it },
                        animationSpec = tween(300)
                    ) + fadeOut(animationSpec = tween(300)),
                    modifier = Modifier.align(Alignment.BottomCenter)
                ) {
                    MediaViewerBottomBar(
                        currentItem = uiState.currentMediaItem,
                        canNavigatePrevious = uiState.canNavigatePrevious,
                        canNavigateNext = uiState.canNavigateNext,
                        isVideoPlaying = uiState.isVideoPlaying,
                        videoProgress = uiState.videoProgress,
                        videoPosition = uiState.videoPosition,
                        videoDuration = uiState.videoDuration,
                        onPrevious = { viewModel.onEvent(MediaViewerUiEvent.NavigatePrevious) },
                        onNext = { viewModel.onEvent(MediaViewerUiEvent.NavigateNext) },
                        onPlayPause = { viewModel.onEvent(MediaViewerUiEvent.ToggleVideoPlayback) },
                        onSeek = { position -> viewModel.onEvent(MediaViewerUiEvent.SeekVideo(position)) },
                        onInfo = { viewModel.onEvent(MediaViewerUiEvent.ToggleInfoOverlay) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // Media info overlay
                AnimatedVisibility(
                    visible = uiState.isInfoOverlayVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(300)
                    ) + fadeIn(animationSpec = tween(300)),
                    exit = slideOutVertically(
                        targetOffsetY = { it },
                        animationSpec = tween(300)
                    ) + fadeOut(animationSpec = tween(300)),
                    modifier = Modifier.align(Alignment.BottomCenter)
                ) {
                    MediaInfoOverlay(
                        mediaItem = uiState.currentMediaItem,
                        onDismiss = { viewModel.onEvent(MediaViewerUiEvent.ToggleInfoOverlay) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * Media Viewer Screen with permission gate
 */
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun MediaViewerScreenWithPermissionGate(
    startingItemId: String,
    onNavigateBack: () -> Unit = {},
    onShowMessage: (String) -> Unit = {},
    onShowError: (String) -> Unit = {},
    sharedTransitionScope: SharedTransitionScope? = null,
    animatedVisibilityScope: AnimatedVisibilityScope? = null
) {
    // For now, directly show the media viewer
    // In a real implementation, you might want to add permission checking here
    MediaViewerScreen(
        startingItemId = startingItemId,
        onNavigateBack = onNavigateBack,
        onShowMessage = onShowMessage,
        onShowError = onShowError,
        sharedTransitionScope = sharedTransitionScope,
        animatedVisibilityScope = animatedVisibilityScope
    )
}
