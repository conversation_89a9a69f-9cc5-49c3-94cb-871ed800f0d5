package com.alpha.gallery.core.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.graphics.FilterQuality
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import coil.ImageLoader
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import coil.request.ImageRequest
import com.alpha.gallery.core.common.di.ThumbnailImageLoader
import com.alpha.gallery.core.domain.model.MediaItem
import javax.inject.Inject

/**
 * Optimized AsyncImage component that can use different ImageLoaders
 * based on the use case (thumbnails vs full images)
 */
@Composable
fun OptimizedAsyncImage(
    mediaItem: MediaItem,
    modifier: Modifier = Modifier,
    imageLoader: ImageLoader? = null,
    useThumbnailLoader: Boolean = false,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    filterQuality: FilterQuality = DrawScope.DefaultFilterQuality,
    crossfade: Boolean = true,
    onState: ((AsyncImagePainter.State) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null
) {
    val context = LocalContext.current
    
    if (imageLoader != null) {
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(mediaItem.uri)
                .crossfade(crossfade)
                .apply {
                    // Add size constraints for thumbnails
                    if (useThumbnailLoader) {
                        size(400, 400) // Limit thumbnail size
                        allowHardware(false) // Disable hardware bitmaps for thumbnails
                    }
                }
                .build(),
            imageLoader = imageLoader,
            contentDescription = contentDescription ?: mediaItem.name,
            modifier = modifier,
            contentScale = contentScale,
            alpha = alpha,
            colorFilter = colorFilter,
            filterQuality = filterQuality,
            onLoading = onLoading,
            onSuccess = onSuccess,
            onError = onError
        )
    } else {
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(mediaItem.uri)
                .crossfade(crossfade)
                .apply {
                    // Add size constraints for thumbnails
                    if (useThumbnailLoader) {
                        size(400, 400) // Limit thumbnail size
                        allowHardware(false) // Disable hardware bitmaps for thumbnails
                    }
                }
                .build(),
            contentDescription = contentDescription ?: mediaItem.name,
            modifier = modifier,
            contentScale = contentScale,
            alpha = alpha,
            colorFilter = colorFilter,
            filterQuality = filterQuality,
            onLoading = onLoading,
            onSuccess = onSuccess,
            onError = onError
        )
    }
}

/**
 * Thumbnail-optimized AsyncImage component
 */
@Composable
fun ThumbnailAsyncImage(
    mediaItem: MediaItem,
    modifier: Modifier = Modifier,
    thumbnailImageLoader: ImageLoader? = null,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Crop,
    onState: ((AsyncImagePainter.State) -> Unit)? = null
) {
    OptimizedAsyncImage(
        mediaItem = mediaItem,
        modifier = modifier,
        imageLoader = thumbnailImageLoader,
        useThumbnailLoader = true,
        contentDescription = contentDescription,
        contentScale = contentScale,
        crossfade = false, // Disable crossfade for faster thumbnail loading
        onState = onState
    )
}

/**
 * Full-size image AsyncImage component
 */
@Composable
fun FullSizeAsyncImage(
    mediaItem: MediaItem,
    modifier: Modifier = Modifier,
    imageLoader: ImageLoader? = null,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit,
    crossfade: Boolean = true,
    onState: ((AsyncImagePainter.State) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    OptimizedAsyncImage(
        mediaItem = mediaItem,
        modifier = modifier,
        imageLoader = imageLoader,
        useThumbnailLoader = false,
        contentDescription = contentDescription,
        contentScale = contentScale,
        crossfade = crossfade,
        onState = onState,
        onSuccess = onSuccess,
        onError = onError
    )
}

/**
 * Helper class for managing ImageLoaders in components
 */
class ImageLoaderProvider @Inject constructor(
    private val defaultImageLoader: ImageLoader,
    @ThumbnailImageLoader private val thumbnailImageLoader: ImageLoader
) {
    
    /**
     * Get the appropriate ImageLoader for the use case
     */
    fun getImageLoader(useThumbnailLoader: Boolean): ImageLoader {
        return if (useThumbnailLoader) thumbnailImageLoader else defaultImageLoader
    }
    
    /**
     * Get the default ImageLoader
     */
    fun getDefaultImageLoader(): ImageLoader = defaultImageLoader
    
    /**
     * Get the thumbnail ImageLoader
     */
    fun getThumbnailImageLoader(): ImageLoader = thumbnailImageLoader
}
