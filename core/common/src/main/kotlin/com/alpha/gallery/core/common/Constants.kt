package com.alpha.gallery.core.common

object Constants {
    // Database
    const val DATABASE_NAME = "gallery_database"
    const val DATABASE_VERSION = 1
    
    // Network
    const val NETWORK_TIMEOUT = 30L
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    
    // Media
    const val SUPPORTED_IMAGE_EXTENSIONS = "jpg,jpeg,png,gif,webp,bmp"
    const val SUPPORTED_VIDEO_EXTENSIONS = "mp4,avi,mov,mkv,wmv,flv,webm"
    
    // Preferences
    const val PREFERENCES_NAME = "gallery_preferences"
    
    // Grid
    const val DEFAULT_GRID_COLUMNS = 3
    const val MIN_GRID_COLUMNS = 2
    const val MAX_GRID_COLUMNS = 6
    
    // Cache
    const val IMAGE_CACHE_SIZE = 100 * 1024 * 1024L // 100MB
    const val THUMBNAIL_CACHE_SIZE = 50 * 1024 * 1024L // 50MB

    // BlobCache Configuration
    const val BLOB_CACHE_MEMORY_SIZE = 64 * 1024 * 1024L // 64MB
    const val BLOB_CACHE_DISK_SIZE = 200 * 1024 * 1024L // 200MB
    const val BLOB_CACHE_THUMBNAIL_MEMORY_SIZE = 32 * 1024 * 1024L // 32MB
    const val BLOB_CACHE_THUMBNAIL_DISK_SIZE = 100 * 1024 * 1024L // 100MB

    // Coil Cache Configuration
    const val COIL_MEMORY_CACHE_SIZE = 0.25 // 25% of available memory
    const val COIL_DISK_CACHE_SIZE = 250 * 1024 * 1024L // 250MB
    
    // Permissions
    const val PERMISSION_REQUEST_CODE = 1001
}
