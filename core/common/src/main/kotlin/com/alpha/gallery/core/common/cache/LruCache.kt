package com.alpha.gallery.core.common.cache

import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * A thread-safe LRU (Least Recently Used) cache implementation.
 * 
 * This cache maintains a maximum size and evicts the least recently used entries
 * when the cache exceeds its capacity. It's optimized for concurrent access
 * with separate read and write locks.
 */
class LruCache<K, V>(private val maxSize: Long) {
    
    private val lock = ReentrantReadWriteLock()
    private val cache = LinkedHashMap<K, CacheEntry<V>>(16, 0.75f, true)
    
    @Volatile
    private var currentSize = 0L
    
    @Volatile
    private var hitCount = 0L
    
    @Volatile
    private var missCount = 0L
    
    /**
     * Store a value in the cache
     */
    fun put(key: K, value: V): V? {
        val size = sizeOf(key, value)
        if (size <= 0) {
            throw IllegalArgumentException("Size must be positive")
        }
        
        return lock.write {
            val entry = CacheEntry(value, size)
            val previous = cache.put(key, entry)
            
            if (previous != null) {
                currentSize -= previous.size
            }
            currentSize += size
            
            // Ensure cache doesn't exceed max size
            trimToSize()
            
            previous?.value
        }
    }
    
    /**
     * Retrieve a value from the cache
     */
    fun get(key: K): V? {
        return lock.read {
            val entry = cache[key]
            if (entry != null) {
                hitCount++
                entry.value
            } else {
                missCount++
                null
            }
        }
    }
    
    /**
     * Remove a value from the cache
     */
    fun remove(key: K): V? {
        return lock.write {
            val entry = cache.remove(key)
            if (entry != null) {
                currentSize -= entry.size
                entry.value
            } else {
                null
            }
        }
    }
    
    /**
     * Check if the cache contains a key
     */
    fun contains(key: K): Boolean {
        return lock.read {
            cache.containsKey(key)
        }
    }
    
    /**
     * Clear all entries from the cache
     */
    fun clear() {
        lock.write {
            cache.clear()
            currentSize = 0L
        }
    }
    
    /**
     * Get the current size of the cache
     */
    fun size(): Long = currentSize
    
    /**
     * Get the maximum size of the cache
     */
    fun maxSize(): Long = maxSize
    
    /**
     * Get the number of entries in the cache
     */
    fun entryCount(): Int {
        return lock.read {
            cache.size
        }
    }
    
    /**
     * Get the hit count
     */
    fun hitCount(): Long = hitCount
    
    /**
     * Get the miss count
     */
    fun missCount(): Long = missCount
    
    /**
     * Get the hit rate
     */
    fun hitRate(): Float {
        val total = hitCount + missCount
        return if (total > 0) hitCount.toFloat() / total else 0f
    }
    
    /**
     * Get all keys in the cache (for debugging)
     */
    fun keys(): Set<K> {
        return lock.read {
            cache.keys.toSet()
        }
    }
    
    /**
     * Trim the cache to ensure it doesn't exceed maxSize
     */
    private fun trimToSize() {
        while (currentSize > maxSize && cache.isNotEmpty()) {
            val eldest = cache.entries.iterator().next()
            val key = eldest.key
            val entry = eldest.value
            
            cache.remove(key)
            currentSize -= entry.size
            
            // Notify about eviction (can be overridden by subclasses)
            onEntryEvicted(key, entry.value)
        }
    }
    
    /**
     * Calculate the size of an entry. Default implementation assumes each entry is 1 unit.
     * Override this method to provide custom sizing logic.
     */
    protected open fun sizeOf(key: K, value: V): Long {
        return when (value) {
            is ByteArray -> value.size.toLong()
            is String -> value.length.toLong()
            else -> 1L
        }
    }
    
    /**
     * Called when an entry is evicted from the cache.
     * Override this method to perform cleanup operations.
     */
    protected open fun onEntryEvicted(key: K, value: V) {
        // Default implementation does nothing
    }
    
    /**
     * Get cache statistics
     */
    fun getStats(): CacheStats {
        return lock.read {
            CacheStats(
                size = currentSize,
                maxSize = maxSize,
                entryCount = cache.size,
                hitCount = hitCount,
                missCount = missCount,
                hitRate = hitRate()
            )
        }
    }
    
    /**
     * Cache entry wrapper
     */
    private data class CacheEntry<V>(
        val value: V,
        val size: Long
    )
    
    /**
     * Cache statistics data class
     */
    data class CacheStats(
        val size: Long,
        val maxSize: Long,
        val entryCount: Int,
        val hitCount: Long,
        val missCount: Long,
        val hitRate: Float
    )
    
    override fun toString(): String {
        return lock.read {
            "LruCache[size=$currentSize, maxSize=$maxSize, entries=${cache.size}, " +
                    "hits=$hitCount, misses=$missCount, hitRate=${String.format("%.2f", hitRate() * 100)}%]"
        }
    }
}
