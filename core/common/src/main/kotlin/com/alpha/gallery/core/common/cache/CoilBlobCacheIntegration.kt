package com.alpha.gallery.core.common.cache

import android.content.Context
import android.graphics.Bitmap
import coil.disk.DiskCache
import coil.memory.MemoryCache
import kotlinx.coroutines.runBlocking
import okio.*
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream

/**
 * Coil MemoryCache implementation backed by BlobCache
 */
class BlobMemoryCache(
    private val blobCache: BlobCache
) : MemoryCache {

    override val maxSize: Int = Int.MAX_VALUE // BlobCache handles size internally
    override val size: Int get() = blobCache.getMemorySize().toInt()

    override fun get(key: MemoryCache.Key): MemoryCache.Value? {
        return runBlocking {
            val data = blobCache.get(key.key) ?: return@runBlocking null

            try {
                // Deserialize bitmap from byte array
                val bitmap = data.toBitmap()
                MemoryCache.Value(bitmap, mapOf())
            } catch (e: Exception) {
                null
            }
        }
    }

    override fun set(key: MemoryCache.Key, value: MemoryCache.Value) {
        runBlocking {
            try {
                // Serialize bitmap to byte array
                val bitmap = value.bitmap
                val data = bitmap.toByteArray()
                blobCache.put(key.key, data)
            } catch (e: Exception) {
                // Ignore serialization errors
            }
        }
    }

    override fun remove(key: MemoryCache.Key): Boolean {
        return runBlocking {
            blobCache.remove(key.key)
        }
    }

    override fun clear() {
        runBlocking {
            blobCache.clear()
        }
    }

    override fun trimMemory(level: Int) {
        // BlobCache handles its own memory management
        // We could implement trimming based on level if needed
    }

    override val keys: Set<MemoryCache.Key>
        get() = emptySet() // Not supported by BlobCache

    private fun ByteArray.toBitmap(): Bitmap {
        val inputStream = ByteArrayInputStream(this)
        return android.graphics.BitmapFactory.decodeStream(inputStream)
            ?: throw IllegalArgumentException("Invalid bitmap data")
    }

    private fun Bitmap.toByteArray(): ByteArray {
        val outputStream = ByteArrayOutputStream()
        compress(Bitmap.CompressFormat.PNG, 100, outputStream)
        return outputStream.toByteArray()
    }
}





/**
 * Factory for creating Coil cache instances backed by BlobCache
 */
object CoilBlobCacheFactory {

    /**
     * Create a MemoryCache backed by BlobCache
     */
    fun createMemoryCache(context: Context, maxSize: Long): MemoryCache {
        val blobCache = BlobCache.create(
            context = context,
            maxMemorySize = maxSize,
            maxDiskSize = 0L, // Memory cache only
            cacheDir = "coil_memory_cache"
        )
        return BlobMemoryCache(blobCache)
    }

    /**
     * Create a DiskCache using Coil's default implementation
     * but with custom directory backed by our BlobCache directory structure
     */
    fun createDiskCache(context: Context, maxSize: Long): DiskCache {
        return DiskCache.Builder()
            .directory(context.cacheDir.resolve("coil_blob_disk_cache"))
            .maxSizeBytes(maxSize)
            .build()
    }

    /**
     * Create both memory and disk caches
     */
    fun createCaches(
        context: Context,
        maxMemorySize: Long,
        maxDiskSize: Long
    ): Pair<MemoryCache, DiskCache> {
        return Pair(
            createMemoryCache(context, maxMemorySize),
            createDiskCache(context, maxDiskSize)
        )
    }

    /**
     * Create a complete ImageLoader with BlobCache integration
     */
    fun createImageLoader(context: Context): coil.ImageLoader {
        val memoryCache = createMemoryCache(context, calculateMemoryCacheSize(context))
        val diskCache = createDiskCache(context, com.alpha.gallery.core.common.Constants.COIL_DISK_CACHE_SIZE)

        return coil.ImageLoader.Builder(context)
            .memoryCache(memoryCache)
            .diskCache(diskCache)
            .components {
                // Add support for GIFs
                if (android.os.Build.VERSION.SDK_INT >= 28) {
                    add(coil.decode.ImageDecoderDecoder.Factory())
                } else {
                    add(coil.decode.GifDecoder.Factory())
                }

                // Add support for video thumbnails
                add(coil.decode.VideoFrameDecoder.Factory())
            }
            .memoryCachePolicy(coil.request.CachePolicy.ENABLED)
            .diskCachePolicy(coil.request.CachePolicy.ENABLED)
            .networkCachePolicy(coil.request.CachePolicy.ENABLED)
            .respectCacheHeaders(false) // We manage our own cache
            .allowHardware(true) // Enable hardware bitmaps for better performance
            .allowRgb565(true) // Allow RGB_565 for smaller memory footprint
            .crossfade(true) // Enable crossfade animations
            .crossfade(300) // 300ms crossfade duration
            .apply {
                // Enable debug logging in debug builds
                if (android.util.Log.isLoggable("Coil", android.util.Log.DEBUG)) {
                    logger(coil.util.DebugLogger())
                }
            }
            .build()
    }

    /**
     * Calculate optimal memory cache size based on available memory
     */
    private fun calculateMemoryCacheSize(context: Context): Long {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        val memoryInfo = android.app.ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        // Use percentage of available memory, but cap it at our defined maximum
        val availableMemory = memoryInfo.availMem
        val calculatedSize = (availableMemory * com.alpha.gallery.core.common.Constants.COIL_MEMORY_CACHE_SIZE).toLong()

        return minOf(calculatedSize, com.alpha.gallery.core.common.Constants.BLOB_CACHE_MEMORY_SIZE)
    }
}
