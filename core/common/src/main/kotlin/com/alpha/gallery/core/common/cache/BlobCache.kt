package com.alpha.gallery.core.common.cache

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.*
import java.security.MessageDigest
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.min

/**
 * A blob cache implementation similar to Android Gallery2's BlobCache.
 * Provides both memory and disk caching with LRU eviction policy.
 * 
 * This cache is designed for efficient storage and retrieval of binary data (blobs)
 * such as images, thumbnails, and other media content.
 */
class BlobCache private constructor(
    private val context: Context,
    private val maxMemorySize: Long,
    private val maxDiskSize: Long,
    private val diskCacheDir: File
) {
    companion object {
        private const val TAG = "BlobCache"
        private const val DISK_CACHE_VERSION = 1
        private const val DISK_CACHE_INDEX_FILE = "cache_index"
        private const val TEMP_FILE_SUFFIX = ".tmp"
        
        /**
         * Create a new BlobCache instance
         */
        fun create(
            context: Context,
            maxMemorySize: Long,
            maxDiskSize: Long,
            cacheDir: String = "blob_cache"
        ): BlobCache {
            val diskCacheDir = File(context.cacheDir, cacheDir)
            if (!diskCacheDir.exists()) {
                diskCacheDir.mkdirs()
            }
            return BlobCache(context, maxMemorySize, maxDiskSize, diskCacheDir)
        }
    }
    
    // Memory cache using LRU policy
    private val memoryCache = LruCache<String, ByteArray>(maxMemorySize)
    
    // Disk cache metadata
    private val diskCacheIndex = ConcurrentHashMap<String, DiskCacheEntry>()
    private val diskCacheMutex = Mutex()
    
    // Current disk cache size
    @Volatile
    private var currentDiskSize = 0L
    
    init {
        loadDiskCacheIndex()
    }
    
    /**
     * Store data in the cache
     */
    suspend fun put(key: String, data: ByteArray): Boolean {
        return try {
            val hashedKey = hashKey(key)
            
            // Store in memory cache
            memoryCache.put(hashedKey, data)
            
            // Store in disk cache
            putToDisk(hashedKey, data)
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to put data for key: $key", e)
            false
        }
    }
    
    /**
     * Retrieve data from the cache
     */
    suspend fun get(key: String): ByteArray? {
        return try {
            val hashedKey = hashKey(key)
            
            // Try memory cache first
            memoryCache.get(hashedKey)?.let { return it }
            
            // Try disk cache
            getFromDisk(hashedKey)?.also { data ->
                // Put back in memory cache
                memoryCache.put(hashedKey, data)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get data for key: $key", e)
            null
        }
    }
    
    /**
     * Check if key exists in cache
     */
    suspend fun contains(key: String): Boolean {
        val hashedKey = hashKey(key)
        return memoryCache.contains(hashedKey) || diskCacheIndex.containsKey(hashedKey)
    }
    
    /**
     * Remove data from cache
     */
    suspend fun remove(key: String): Boolean {
        return try {
            val hashedKey = hashKey(key)
            
            // Remove from memory cache
            memoryCache.remove(hashedKey)
            
            // Remove from disk cache
            removeFromDisk(hashedKey)
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to remove data for key: $key", e)
            false
        }
    }
    
    /**
     * Clear all cached data
     */
    suspend fun clear() {
        memoryCache.clear()
        clearDiskCache()
    }
    
    /**
     * Get current memory cache size
     */
    fun getMemorySize(): Long = memoryCache.size()
    
    /**
     * Get current disk cache size
     */
    fun getDiskSize(): Long = currentDiskSize
    
    /**
     * Get cache statistics
     */
    fun getStats(): CacheStats {
        return CacheStats(
            memorySize = memoryCache.size(),
            memoryMaxSize = maxMemorySize,
            memoryHitCount = memoryCache.hitCount(),
            memoryMissCount = memoryCache.missCount(),
            diskSize = currentDiskSize,
            diskMaxSize = maxDiskSize,
            diskEntryCount = diskCacheIndex.size
        )
    }
    
    private fun hashKey(key: String): String {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            val hash = digest.digest(key.toByteArray())
            hash.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            // Fallback to simple hash if MD5 is not available
            key.hashCode().toString()
        }
    }
    
    private suspend fun putToDisk(key: String, data: ByteArray): Boolean = withContext(Dispatchers.IO) {
        diskCacheMutex.withLock {
            try {
                val file = File(diskCacheDir, key)
                val tempFile = File(diskCacheDir, "$key$TEMP_FILE_SUFFIX")
                
                // Write to temp file first
                tempFile.outputStream().use { output ->
                    output.write(data)
                    output.flush()
                }
                
                // Atomic rename
                if (tempFile.renameTo(file)) {
                    val entry = DiskCacheEntry(
                        key = key,
                        size = data.size.toLong(),
                        lastAccessed = System.currentTimeMillis()
                    )
                    
                    // Update index
                    val oldEntry = diskCacheIndex.put(key, entry)
                    currentDiskSize += entry.size - (oldEntry?.size ?: 0L)
                    
                    // Ensure disk cache size limit
                    ensureDiskCacheSize()
                    
                    // Save index
                    saveDiskCacheIndex()
                    
                    true
                } else {
                    tempFile.delete()
                    false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to write to disk cache", e)
                false
            }
        }
    }
    
    private suspend fun getFromDisk(key: String): ByteArray? = withContext(Dispatchers.IO) {
        diskCacheMutex.withLock {
            try {
                val entry = diskCacheIndex[key] ?: return@withLock null
                val file = File(diskCacheDir, key)
                
                if (!file.exists()) {
                    // File was deleted externally, remove from index
                    diskCacheIndex.remove(key)
                    currentDiskSize -= entry.size
                    return@withLock null
                }
                
                // Update last accessed time
                entry.lastAccessed = System.currentTimeMillis()
                
                // Read file
                file.readBytes()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to read from disk cache", e)
                null
            }
        }
    }
    
    private suspend fun removeFromDisk(key: String): Boolean = withContext(Dispatchers.IO) {
        diskCacheMutex.withLock {
            try {
                val entry = diskCacheIndex.remove(key)
                if (entry != null) {
                    val file = File(diskCacheDir, key)
                    if (file.exists()) {
                        file.delete()
                    }
                    currentDiskSize -= entry.size
                    saveDiskCacheIndex()
                }
                true
            } catch (e: Exception) {
                Log.e(TAG, "Failed to remove from disk cache", e)
                false
            }
        }
    }
    
    private suspend fun clearDiskCache() = withContext(Dispatchers.IO) {
        diskCacheMutex.withLock {
            try {
                diskCacheDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        file.delete()
                    }
                }
                diskCacheIndex.clear()
                currentDiskSize = 0L
                saveDiskCacheIndex()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear disk cache", e)
            }
        }
    }
    
    private fun ensureDiskCacheSize() {
        if (currentDiskSize <= maxDiskSize) return
        
        // Sort entries by last accessed time (LRU)
        val sortedEntries = diskCacheIndex.values.sortedBy { it.lastAccessed }
        
        for (entry in sortedEntries) {
            if (currentDiskSize <= maxDiskSize) break
            
            try {
                val file = File(diskCacheDir, entry.key)
                if (file.exists()) {
                    file.delete()
                }
                diskCacheIndex.remove(entry.key)
                currentDiskSize -= entry.size
            } catch (e: Exception) {
                Log.e(TAG, "Failed to evict disk cache entry: ${entry.key}", e)
            }
        }
    }
    
    private fun loadDiskCacheIndex() {
        try {
            val indexFile = File(diskCacheDir, DISK_CACHE_INDEX_FILE)
            if (!indexFile.exists()) return
            
            ObjectInputStream(FileInputStream(indexFile)).use { input ->
                val version = input.readInt()
                if (version != DISK_CACHE_VERSION) return
                
                val size = input.readInt()
                repeat(size) {
                    val key = input.readUTF()
                    val entrySize = input.readLong()
                    val lastAccessed = input.readLong()
                    
                    val entry = DiskCacheEntry(key, entrySize, lastAccessed)
                    diskCacheIndex[key] = entry
                    currentDiskSize += entrySize
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to load disk cache index, starting fresh", e)
            diskCacheIndex.clear()
            currentDiskSize = 0L
        }
    }
    
    private fun saveDiskCacheIndex() {
        try {
            val indexFile = File(diskCacheDir, DISK_CACHE_INDEX_FILE)
            val tempFile = File(diskCacheDir, "$DISK_CACHE_INDEX_FILE$TEMP_FILE_SUFFIX")
            
            ObjectOutputStream(FileOutputStream(tempFile)).use { output ->
                output.writeInt(DISK_CACHE_VERSION)
                output.writeInt(diskCacheIndex.size)
                
                diskCacheIndex.values.forEach { entry ->
                    output.writeUTF(entry.key)
                    output.writeLong(entry.size)
                    output.writeLong(entry.lastAccessed)
                }
                
                output.flush()
            }
            
            tempFile.renameTo(indexFile)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save disk cache index", e)
        }
    }
    
    /**
     * Disk cache entry metadata
     */
    private data class DiskCacheEntry(
        val key: String,
        val size: Long,
        var lastAccessed: Long
    )
    
    /**
     * Cache statistics
     */
    data class CacheStats(
        val memorySize: Long,
        val memoryMaxSize: Long,
        val memoryHitCount: Long,
        val memoryMissCount: Long,
        val diskSize: Long,
        val diskMaxSize: Long,
        val diskEntryCount: Int
    ) {
        val memoryHitRate: Float
            get() = if (memoryHitCount + memoryMissCount > 0) {
                memoryHitCount.toFloat() / (memoryHitCount + memoryMissCount)
            } else 0f
    }
}
