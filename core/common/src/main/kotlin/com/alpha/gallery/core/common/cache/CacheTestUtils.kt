package com.alpha.gallery.core.common.cache

import android.content.Context
import android.util.Log
import coil.ImageLoader
import coil.request.ImageRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Utility class for testing and debugging cache functionality
 */
object CacheTestUtils {
    private const val TAG = "CacheTestUtils"
    
    /**
     * Test BlobCache functionality
     */
    suspend fun testBlobCache(context: Context): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting BlobCache test...")
            
            val blobCache = BlobCache.create(
                context = context,
                maxMemorySize = 10 * 1024 * 1024L, // 10MB
                maxDiskSize = 20 * 1024 * 1024L, // 20MB
                cacheDir = "test_blob_cache"
            )
            
            // Test data
            val testKey = "test_key_123"
            val testData = "Hello, BlobCache! This is test data.".toByteArray()
            
            // Test put operation
            val putResult = blobCache.put(testKey, testData)
            Log.d(TAG, "Put operation result: $putResult")
            
            // Test get operation
            val retrievedData = blobCache.get(testKey)
            val getResult = retrievedData != null && retrievedData.contentEquals(testData)
            Log.d(TAG, "Get operation result: $getResult")
            
            // Test contains operation
            val containsResult = blobCache.contains(testKey)
            Log.d(TAG, "Contains operation result: $containsResult")
            
            // Test cache stats
            val stats = blobCache.getStats()
            Log.d(TAG, "Cache stats: $stats")
            
            // Test remove operation
            val removeResult = blobCache.remove(testKey)
            Log.d(TAG, "Remove operation result: $removeResult")
            
            // Verify removal
            val afterRemoveData = blobCache.get(testKey)
            val removalVerified = afterRemoveData == null
            Log.d(TAG, "Removal verified: $removalVerified")
            
            val allTestsPassed = putResult && getResult && containsResult && removeResult && removalVerified
            Log.d(TAG, "BlobCache test completed. All tests passed: $allTestsPassed")
            
            allTestsPassed
        } catch (e: Exception) {
            Log.e(TAG, "BlobCache test failed", e)
            false
        }
    }
    
    /**
     * Test Coil integration with BlobCache
     */
    suspend fun testCoilIntegration(context: Context, imageLoader: ImageLoader): Boolean = withContext(Dispatchers.Main) {
        try {
            Log.d(TAG, "Starting Coil integration test...")
            
            // Create a simple test request
            val request = ImageRequest.Builder(context)
                .data("android.resource://${context.packageName}/drawable/ic_launcher_foreground")
                .build()
            
            // Execute the request
            val result = imageLoader.execute(request)
            val success = result.drawable != null
            
            Log.d(TAG, "Coil integration test result: $success")
            
            // Log cache stats if available
            logCacheStats(imageLoader)
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "Coil integration test failed", e)
            false
        }
    }
    
    /**
     * Log cache statistics for debugging
     */
    fun logCacheStats(imageLoader: ImageLoader) {
        try {
            val memoryCache = imageLoader.memoryCache
            val diskCache = imageLoader.diskCache
            
            Log.d(TAG, "Memory cache - Size: ${memoryCache?.size}, Max: ${memoryCache?.maxSize}")
            Log.d(TAG, "Disk cache - Size: ${diskCache?.size}, Max: ${diskCache?.maxSize}")
            
            // If using BlobMemoryCache, try to get additional stats
            if (memoryCache is BlobMemoryCache) {
                // Additional logging could be added here if needed
                Log.d(TAG, "Using BlobMemoryCache implementation")
            }
            
            if (diskCache is BlobDiskCache) {
                Log.d(TAG, "Using BlobDiskCache implementation")
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to log cache stats", e)
        }
    }
    
    /**
     * Benchmark cache performance
     */
    suspend fun benchmarkCache(context: Context, iterations: Int = 100): CacheBenchmarkResult = withContext(Dispatchers.IO) {
        val blobCache = BlobCache.create(
            context = context,
            maxMemorySize = 50 * 1024 * 1024L, // 50MB
            maxDiskSize = 100 * 1024 * 1024L, // 100MB
            cacheDir = "benchmark_blob_cache"
        )
        
        val testData = ByteArray(1024) { it.toByte() } // 1KB test data
        
        // Benchmark put operations
        val putStartTime = System.currentTimeMillis()
        repeat(iterations) { i ->
            blobCache.put("benchmark_key_$i", testData)
        }
        val putDuration = System.currentTimeMillis() - putStartTime
        
        // Benchmark get operations
        val getStartTime = System.currentTimeMillis()
        repeat(iterations) { i ->
            blobCache.get("benchmark_key_$i")
        }
        val getDuration = System.currentTimeMillis() - getStartTime
        
        val stats = blobCache.getStats()
        
        CacheBenchmarkResult(
            iterations = iterations,
            putDurationMs = putDuration,
            getDurationMs = getDuration,
            avgPutTimeMs = putDuration.toDouble() / iterations,
            avgGetTimeMs = getDuration.toDouble() / iterations,
            finalStats = stats
        )
    }
    
    /**
     * Clear all test caches
     */
    suspend fun clearTestCaches(context: Context) = withContext(Dispatchers.IO) {
        try {
            val testCacheDirs = listOf(
                "test_blob_cache",
                "benchmark_blob_cache"
            )
            
            testCacheDirs.forEach { cacheDir ->
                val dir = java.io.File(context.cacheDir, cacheDir)
                if (dir.exists()) {
                    dir.deleteRecursively()
                    Log.d(TAG, "Cleared test cache directory: $cacheDir")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear test caches", e)
        }
    }
}

/**
 * Result of cache benchmark test
 */
data class CacheBenchmarkResult(
    val iterations: Int,
    val putDurationMs: Long,
    val getDurationMs: Long,
    val avgPutTimeMs: Double,
    val avgGetTimeMs: Double,
    val finalStats: BlobCache.CacheStats
) {
    override fun toString(): String {
        return """
            Cache Benchmark Results:
            - Iterations: $iterations
            - Put operations: ${putDurationMs}ms total, ${String.format("%.2f", avgPutTimeMs)}ms avg
            - Get operations: ${getDurationMs}ms total, ${String.format("%.2f", avgGetTimeMs)}ms avg
            - Final cache stats: $finalStats
        """.trimIndent()
    }
}
