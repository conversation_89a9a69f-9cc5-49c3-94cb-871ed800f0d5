package com.alpha.gallery.core.common.di

import android.app.ActivityManager
import android.content.Context
import coil.ImageLoader
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import coil.decode.VideoFrameDecoder
import coil.disk.DiskCache
import coil.memory.MemoryCache
import coil.request.CachePolicy
import coil.util.DebugLogger
import com.alpha.gallery.core.common.Constants
import com.alpha.gallery.core.common.cache.BlobCache
import com.alpha.gallery.core.common.cache.CoilBlobCacheFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * Hilt module for image loading dependencies using BlobCache
 */
@Module
@InstallIn(SingletonComponent::class)
object ImageLoadingModule {
    
    /**
     * Provide the main BlobCache for images
     */
    @Provides
    @Singleton
    @ImageBlobCache
    fun provideImageBlobCache(@ApplicationContext context: Context): BlobCache {
        return BlobCache.create(
            context = context,
            maxMemorySize = Constants.BLOB_CACHE_MEMORY_SIZE,
            maxDiskSize = Constants.BLOB_CACHE_DISK_SIZE,
            cacheDir = "image_blob_cache"
        )
    }
    
    /**
     * Provide the thumbnail BlobCache
     */
    @Provides
    @Singleton
    @ThumbnailBlobCache
    fun provideThumbnailBlobCache(@ApplicationContext context: Context): BlobCache {
        return BlobCache.create(
            context = context,
            maxMemorySize = Constants.BLOB_CACHE_THUMBNAIL_MEMORY_SIZE,
            maxDiskSize = Constants.BLOB_CACHE_THUMBNAIL_DISK_SIZE,
            cacheDir = "thumbnail_blob_cache"
        )
    }
    
    /**
     * Provide Coil MemoryCache backed by BlobCache
     */
    @Provides
    @Singleton
    fun provideCoilMemoryCache(@ApplicationContext context: Context): MemoryCache {
        val memorySize = calculateMemoryCacheSize(context)
        return CoilBlobCacheFactory.createMemoryCache(context, memorySize)
    }
    
    /**
     * Provide Coil DiskCache using Coil's built-in implementation
     */
    @Provides
    @Singleton
    fun provideCoilDiskCache(@ApplicationContext context: Context): DiskCache {
        return CoilBlobCacheFactory.createDiskCache(context, Constants.COIL_DISK_CACHE_SIZE)
    }
    
    /**
     * Provide the main ImageLoader with BlobCache integration
     */
    @Provides
    @Singleton
    fun provideImageLoader(
        @ApplicationContext context: Context,
        memoryCache: MemoryCache,
        diskCache: DiskCache
    ): ImageLoader {
        return ImageLoader.Builder(context)
            .memoryCache(memoryCache)
            .diskCache(diskCache)
            .components {
                // Add support for GIFs
                if (android.os.Build.VERSION.SDK_INT >= 28) {
                    add(ImageDecoderDecoder.Factory())
                } else {
                    add(GifDecoder.Factory())
                }
                
                // Add support for video thumbnails
                add(VideoFrameDecoder.Factory())
            }
            .memoryCachePolicy(CachePolicy.ENABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            .networkCachePolicy(CachePolicy.ENABLED)
            .respectCacheHeaders(false) // We manage our own cache
            .allowHardware(true) // Enable hardware bitmaps for better performance
            .allowRgb565(true) // Allow RGB_565 for smaller memory footprint
            .crossfade(true) // Enable crossfade animations
            .crossfade(300) // 300ms crossfade duration
            .apply {
                // Enable debug logging in debug builds
                if (android.util.Log.isLoggable("Coil", android.util.Log.DEBUG)) {
                    logger(DebugLogger())
                }
            }
            .build()
    }
    
    /**
     * Provide a separate ImageLoader for thumbnails with optimized settings
     */
    @Provides
    @Singleton
    @ThumbnailImageLoader
    fun provideThumbnailImageLoader(
        @ApplicationContext context: Context,
        @ThumbnailBlobCache thumbnailBlobCache: BlobCache
    ): ImageLoader {
        val (memoryCache, diskCache) = CoilBlobCacheFactory.createCaches(
            context = context,
            maxMemorySize = Constants.BLOB_CACHE_THUMBNAIL_MEMORY_SIZE,
            maxDiskSize = Constants.BLOB_CACHE_THUMBNAIL_DISK_SIZE
        )
        
        return ImageLoader.Builder(context)
            .memoryCache(memoryCache)
            .diskCache(diskCache)
            .components {
                // Add support for GIFs
                if (android.os.Build.VERSION.SDK_INT >= 28) {
                    add(ImageDecoderDecoder.Factory())
                } else {
                    add(GifDecoder.Factory())
                }
                
                // Add support for video thumbnails
                add(VideoFrameDecoder.Factory())
            }
            .memoryCachePolicy(CachePolicy.ENABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            .networkCachePolicy(CachePolicy.ENABLED)
            .respectCacheHeaders(false)
            .allowHardware(false) // Disable hardware bitmaps for thumbnails
            .allowRgb565(true) // Use RGB_565 for smaller thumbnails
            .crossfade(false) // Disable crossfade for faster thumbnail loading
            .build()
    }
    
    /**
     * Calculate optimal memory cache size based on available memory
     */
    private fun calculateMemoryCacheSize(context: Context): Long {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        // Use percentage of available memory, but cap it at our defined maximum
        val availableMemory = memoryInfo.availMem
        val calculatedSize = (availableMemory * Constants.COIL_MEMORY_CACHE_SIZE).toLong()
        
        return minOf(calculatedSize, Constants.BLOB_CACHE_MEMORY_SIZE)
    }
}

/**
 * Qualifier for image BlobCache
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ImageBlobCache

/**
 * Qualifier for thumbnail BlobCache
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ThumbnailBlobCache

/**
 * Qualifier for thumbnail ImageLoader
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ThumbnailImageLoader
