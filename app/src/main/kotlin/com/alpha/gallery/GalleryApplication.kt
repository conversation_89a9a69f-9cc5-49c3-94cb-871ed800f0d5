package com.alpha.gallery

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import coil.Coil
import coil.ImageLoader
import com.alpha.gallery.core.common.cache.CacheTestUtils
import com.alpha.gallery.core.common.cache.CoilBlobCacheFactory
import com.alpha.gallery.core.sync.initializer.SyncInitializer
import com.alpha.gallery.core.sync.observer.MediaStoreObserver
import com.alpha.gallery.core.sync.scheduler.SyncWorkScheduler
import com.alpha.gallery.permission.PermissionCoordinator
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Application class for Gallery app with Hilt and WorkManager setup
 */
@HiltAndroidApp
class GalleryApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var syncWorkScheduler: SyncWorkScheduler

    @Inject
    lateinit var mediaStoreObserver: MediaStoreObserver

    @Inject
    lateinit var syncInitializer: SyncInitializer

    @Inject
    lateinit var permissionCoordinator: PermissionCoordinator

    // Application scope for testing
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate() {
        super.onCreate()

        // Initialize Coil with custom BlobCache configuration
        initializeCoil()

        // Test BlobCache implementation (only in debug builds)
        if (android.util.Log.isLoggable("Gallery", android.util.Log.DEBUG)) {
            testBlobCacheImplementation()
        }

        // Initialize sync components with permission awareness
        initializeSync()
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()

    /**
     * Initialize Coil with custom BlobCache configuration
     */
    private fun initializeCoil() {
        // Create ImageLoader with BlobCache integration
        val imageLoader = CoilBlobCacheFactory.createImageLoader(this)
        Coil.setImageLoader(imageLoader)
    }

    /**
     * Test BlobCache implementation in debug builds
     */
    private fun testBlobCacheImplementation() {
        applicationScope.launch {
            try {
                val blobCacheResult = CacheTestUtils.testBlobCache(this@GalleryApplication)
                android.util.Log.d("Gallery", "BlobCache test result: $blobCacheResult")

                val imageLoader = Coil.imageLoader(this@GalleryApplication)
                val coilIntegrationResult = CacheTestUtils.testCoilIntegration(this@GalleryApplication, imageLoader)
                android.util.Log.d("Gallery", "Coil integration test result: $coilIntegrationResult")

                // Log cache stats
                CacheTestUtils.logCacheStats(imageLoader)

            } catch (e: Exception) {
                android.util.Log.e("Gallery", "Failed to test BlobCache implementation", e)
            }
        }
    }

    /**
     * Initialize sync components with permission awareness
     */
    private fun initializeSync() {
        // Initialize sync system (will check permissions internally)
        syncInitializer.initialize()

        // Permission coordinator will handle permission changes and trigger sync
        // No need to manually start observers here as they will be started when permissions are granted
    }

    override fun onTerminate() {
        super.onTerminate()

        // Clean up resources
        mediaStoreObserver.cleanup()
        syncInitializer.cleanup()
        permissionCoordinator.cleanup()

        // Clean up test caches in debug builds
        if (android.util.Log.isLoggable("Gallery", android.util.Log.DEBUG)) {
            applicationScope.launch {
                CacheTestUtils.clearTestCaches(this@GalleryApplication)
            }
        }
    }
}
